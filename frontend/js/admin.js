document.addEventListener('DOMContentLoaded', () => {
    const loginForm = document.getElementById('login-form');
    const adminPanel = document.getElementById('admin-panel');
    const adminContent = document.getElementById('admin-content');
    const adminUsername = document.getElementById('admin-username');
    const logoutBtn = document.getElementById('logout-btn');
    const apiBaseUrl = 'http://localhost:5002/api/AdminAuth';
    const adminApiBaseUrl = 'http://localhost:5002/api/Admin';
    const catManagementApiBaseUrl = 'http://localhost:5002/api/CatManagement';

    // Check for existing token
    const token = localStorage.getItem('admin_token');
    if (token) {
        showAdminPanel(token);
    }

    loginForm.addEventListener('submit', async (e) => {
        e.preventDefault();
        const username = e.target.username.value;
        const password = e.target.password.value;

        try {
            const response = await fetch(`${apiBaseUrl}/login`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ username, password }),
            });

            if (response.ok) {
                const data = await response.json();
                localStorage.setItem('admin_token', data.token);
                showAdminPanel(data.token);
            } else {
                alert('Login failed. Please check your credentials.');
            }
        } catch (error) {
            console.error('Login error:', error);
            alert('An error occurred during login.');
        }
    });

    logoutBtn.addEventListener('click', () => {
        localStorage.removeItem('admin_token');
        adminPanel.style.display = 'none';
        loginForm.style.display = 'block';
        adminContent.innerHTML = '';
    });

    async function showAdminPanel(token) {
        loginForm.style.display = 'none';
        adminPanel.style.display = 'block';

        try {
            const response = await fetch(`${apiBaseUrl}/me`, {
                headers: { Authorization: `Bearer ${token}` },
            });

            if (response.ok) {
                const user = await response.json();
                adminUsername.textContent = user.username;
                // Load the metadata editor
                adminContent.innerHTML = `
                    <div style="text-align: center; padding: 20px;">
                        <h3>Welcome to the Admin Panel</h3>
                        <p>Manage your cattery metadata and photos with the comprehensive editor.</p>
                        <a href="admin-metadata-editor.html" class="btn btn-primary" style="
                            display: inline-block; 
                            padding: 15px 30px; 
                            background: #3498db; 
                            color: white; 
                            text-decoration: none; 
                            border-radius: 5px; 
                            font-weight: bold;
                            margin: 10px;">
                            Open Metadata Editor
                        </a>
                        <div style="margin-top: 30px;">
                            <h4>Quick Stats</h4>
                            <div id="admin-quick-stats">Loading cattery statistics...</div>
                        </div>
                    </div>
                `;
                loadQuickStats();
            } else {
                // Token might be invalid, so log out
                logoutBtn.click();
            }
        } catch (error) {
            console.error('Failed to fetch user info:', error);
            logoutBtn.click();
        }
    }

    async function loadQuickStats() {
        try {
            const token = localStorage.getItem('admin_token');
            const response = await fetch('http://localhost:5002/api/Admin/cats/list-all', {
                headers: { 
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            });

            if (response.ok) {
                const data = await response.json();
                const statsHtml = `
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 15px; max-width: 600px; margin: 0 auto;">
                        <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 15px; border-radius: 8px; text-align: center;">
                            <div style="font-size: 1.5em; font-weight: bold;">${data.totalCats || 0}</div>
                            <div style="font-size: 0.9em;">Total Cats</div>
                        </div>
                        <div style="background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); color: white; padding: 15px; border-radius: 8px; text-align: center;">
                            <div style="font-size: 1.5em; font-weight: bold;">${data.totalPhotos || 0}</div>
                            <div style="font-size: 0.9em;">Total Photos</div>
                        </div>
                        <div style="background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); color: white; padding: 15px; border-radius: 8px; text-align: center;">
                            <div style="font-size: 1.5em; font-weight: bold;">${data.cats?.filter(cat => cat.breedingStatus === 'available-kitten').length || 0}</div>
                            <div style="font-size: 0.9em;">Available Kittens</div>
                        </div>
                        <div style="background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%); color: white; padding: 15px; border-radius: 8px; text-align: center;">
                            <div style="font-size: 1.5em; font-weight: bold;">${[...new Set(data.cats?.map(cat => cat.bloodline).filter(Boolean))].length || 0}</div>
                            <div style="font-size: 0.9em;">Bloodlines</div>
                        </div>
                    </div>
                `;
                document.getElementById('admin-quick-stats').innerHTML = statsHtml;
            } else {
                document.getElementById('admin-quick-stats').innerHTML = '<p>Unable to load statistics</p>';
            }
        } catch (error) {
            console.error('Error loading quick stats:', error);
            document.getElementById('admin-quick-stats').innerHTML = '<p>Error loading statistics</p>';
        }
    }
});
