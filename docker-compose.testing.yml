<<<<<<< HEAD
services:
  migrations:
    build:
      context: backend/CabUCA.API
      dockerfile: Dockerfile.migrations
    image: 025066273203.dkr.ecr.ap-southeast-2.amazonaws.com/olmate/cabuca-migrations-testing:latest
    env_file:
      - .env
    volumes:
      - sqlite-data:/Data

  api:
    build:
      context: backend/CabUCA.API
      dockerfile: Dockerfile
    image: 025066273203.dkr.ecr.ap-southeast-2.amazonaws.com/olmate/cabuca-api-testing:latest
    env_file:
      - .env
    volumes:
      - sqlite-data:/Data

  admin:
    build:
      context: admin-frontend/
      dockerfile: Dockerfile
    image: 025066273203.dkr.ecr.ap-southeast-2.amazonaws.com/olmate/cabuca-admin-testing:latest
    env_file:
      - .env

volumes:
  sqlite-data:
=======
version: '3.8'

services:
  # Backend API service for testing
  api:
    build:
      context: .
      dockerfile: backend/YendorCats.API/Dockerfile
    image: yendorcats/api-test:latest
    container_name: yendorcats-test-api
    restart: unless-stopped
    ports:
      - "5100:80"
      - "5101:443"
    environment:
      - ASPNETCORE_ENVIRONMENT=Testing
      - ASPNETCORE_URLS=http://+:80;https://+:443
      - AWS__Region=us-west-004
      - AWS__UseCredentialsFromSecrets=false
      - AWS__S3__BucketName=yendor-test
      - AWS__S3__UseDirectS3Urls=true
      # Point to local MinIO instance instead of BackBlaze B2
      - AWS__S3__ServiceUrl=http://minio:9000
      - AWS__S3__PublicUrl=http://minio:9000/yendor-test/{key}
      - AWS__S3__UseCdn=false
      - AWS__S3__AccessKey=minioadmin
      - AWS__S3__SecretKey=minioadmin
      # Use SQLite instead of MariaDB for testing
      - ConnectionStrings__SqliteConnection=Data Source=/app/data/yendorcats-test.db
      - JwtSettings__Secret=TestingSecretKey1234567890123456789012
    volumes:
      - api-test-data:/app/data # SQLite database persistence
      - api-test-logs:/app/Logs
    depends_on:
      - minio
    networks:
      - yendorcats-test-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:80/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # MinIO service (S3-compatible local storage)
  minio:
    image: minio/minio:latest
    container_name: yendorcats-test-minio
    restart: unless-stopped
    ports:
      - "9000:9000"
      - "9001:9001"
    environment:
      - MINIO_ROOT_USER=minioadmin
      - MINIO_ROOT_PASSWORD=minioadmin
    volumes:
      - minio-test-data:/data
    command: server /data --console-address ":9001"
    networks:
      - yendorcats-test-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9000/minio/health/live"]
      interval: 30s
      timeout: 10s
      retries: 3

  # File Upload Service
  uploader:
    build:
      context: ./tools/file-uploader
      dockerfile: Dockerfile
    image: yendorcats/uploader-test:latest
    container_name: yendorcats-test-uploader
    restart: unless-stopped
    ports:
      - "5102:80"
    environment:
      - AWS_S3_BUCKET_NAME=yendor-test
      - AWS_S3_REGION=us-east-1
      - AWS_S3_ENDPOINT=http://minio:9000
      - AWS_S3_ACCESS_KEY=minioadmin
      - AWS_S3_SECRET_KEY=minioadmin
      - API_BASE_URL=http://api
    depends_on:
      - api
      - minio
    networks:
      - yendorcats-test-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:80/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Nginx service to serve frontend files
  nginx:
    image: nginx:alpine
    container_name: yendorcats-test-nginx
    restart: unless-stopped
    ports:
      - "8080:80"
    volumes:
      - ./frontend:/usr/share/nginx/html
      - ./frontend/nginx/nginx.conf:/etc/nginx/conf.d/default.conf
    depends_on:
      - api
      - uploader
    networks:
      - yendorcats-test-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:80"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Create test bucket in MinIO on startup
  createbuckets:
    image: minio/mc
    container_name: yendorcats-test-createbuckets
    depends_on:
      - minio
    networks:
      - yendorcats-test-network
    entrypoint: >
      /bin/sh -c "
      sleep 5;
      /usr/bin/mc config host add myminio http://minio:9000 minioadmin minioadmin;
      /usr/bin/mc mb myminio/yendor-test;
      /usr/bin/mc anonymous set download myminio/yendor-test;
      exit 0;
      "

networks:
  yendorcats-test-network:
    driver: bridge

volumes:
  api-test-data:
  api-test-logs:
  minio-test-data:
>>>>>>> dcb0913 (cat profile implementation. project dockerisation and docker container debugging)
