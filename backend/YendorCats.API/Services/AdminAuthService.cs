using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using System.Security.Cryptography;
using System.Text;
using Microsoft.EntityFrameworkCore;
using Microsoft.IdentityModel.Tokens;
using YendorCats.API.Data;
using YendorCats.API.Models;

namespace YendorCats.API.Services
{
    /// <summary>
    /// Admin authentication service implementation
    /// </summary>
    public class AdminAuthService : IAdminAuthService
    {
        private readonly AppDbContext _context;
        private readonly IConfiguration _configuration;
        private readonly ILogger<AdminAuthService> _logger;

        public AdminAuthService(
            AppDbContext context,
            IConfiguration configuration,
            ILogger<AdminAuthService> logger)
        {
            _context = context;
            _configuration = configuration;
            _logger = logger;
        }

        public async Task<AdminLoginResponse> AuthenticateAsync(string username, string password)
        {
            try
            {
                // Find admin by username or email
                var admin = await _context.AdminUsers
                    .FirstOrDefaultAsync(a => 
                        (a.Username == username || a.Email == username) && 
                        a.IsActive);

                if (admin == null)
                {
                    _logger.LogWarning("Login attempt with invalid username: {Username}", username);
                    return new AdminLoginResponse
                    {
                        Success = false,
                        Message = "Invalid username or password"
                    };
                }

                // Check if account is locked
                if (admin.LockedUntil.HasValue && admin.LockedUntil > DateTime.UtcNow)
                {
                    _logger.LogWarning("Login attempt on locked account: {Username}", username);
                    return new AdminLoginResponse
                    {
                        Success = false,
                        Message = "Account is temporarily locked. Please try again later."
                    };
                }

                // Verify password
                if (!VerifyPassword(password, admin.PasswordHash, admin.PasswordSalt))
                {
                    // Increment failed attempts
                    admin.FailedLoginAttempts++;
                    
                    // Lock account after 5 failed attempts for 30 minutes
                    if (admin.FailedLoginAttempts >= 5)
                    {
                        admin.LockedUntil = DateTime.UtcNow.AddMinutes(30);
                        _logger.LogWarning("Account locked due to failed attempts: {Username}", username);
                    }

                    await _context.SaveChangesAsync();

                    return new AdminLoginResponse
                    {
                        Success = false,
                        Message = "Invalid username or password"
                    };
                }

                // Reset failed attempts on successful login
                admin.FailedLoginAttempts = 0;
                admin.LockedUntil = null;
                admin.LastLoginAt = DateTime.UtcNow;
                await _context.SaveChangesAsync();

                // Generate JWT token
                var token = GenerateJwtToken(admin);
                var expiresAt = DateTime.UtcNow.AddMinutes(
                    _configuration.GetValue<int>("JwtSettings:ExpiryMinutes", 60));

                _logger.LogInformation("Successful admin login: {Username}", username);

                return new AdminLoginResponse
                {
                    Success = true,
                    Token = token,
                    ExpiresAt = expiresAt,
                    User = new AdminUserInfo
                    {
                        Id = admin.Id,
                        Username = admin.Username,
                        Email = admin.Email,
                        Role = admin.Role,
                        LastLoginAt = admin.LastLoginAt
                    }
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during admin authentication for {Username}", username);
                return new AdminLoginResponse
                {
                    Success = false,
                    Message = "An error occurred during authentication"
                };
            }
        }

        public async Task<AdminUser> CreateAdminAsync(string username, string email, string password, string role = AdminRoles.Admin)
        {
            // Check if admin already exists
            var existingAdmin = await _context.AdminUsers
                .FirstOrDefaultAsync(a => a.Username == username || a.Email == email);

            if (existingAdmin != null)
            {
                throw new InvalidOperationException("Admin user with this username or email already exists");
            }

            // Hash password
            var (hashedPassword, salt) = HashPassword(password);

            var admin = new AdminUser
            {
                Username = username,
                Email = email,
                PasswordHash = hashedPassword,
                PasswordSalt = salt,
                Role = role,
                IsActive = true,
                CreatedAt = DateTime.UtcNow
            };

            _context.AdminUsers.Add(admin);
            await _context.SaveChangesAsync();

            _logger.LogInformation("Created new admin user: {Username} with role {Role}", username, role);

            return admin;
        }

        public async Task<AdminUser?> ValidateTokenAsync(string token)
        {
            try
            {
                var tokenHandler = new JwtSecurityTokenHandler();
                var key = Encoding.ASCII.GetBytes(_configuration["JwtSettings:Secret"] ?? "");

                tokenHandler.ValidateToken(token, new TokenValidationParameters
                {
                    ValidateIssuerSigningKey = true,
                    IssuerSigningKey = new SymmetricSecurityKey(key),
                    ValidateIssuer = true,
                    ValidIssuer = _configuration["JwtSettings:Issuer"],
                    ValidateAudience = true,
                    ValidAudience = _configuration["JwtSettings:Audience"],
                    ValidateLifetime = true,
                    ClockSkew = TimeSpan.Zero
                }, out SecurityToken validatedToken);

                var jwtToken = (JwtSecurityToken)validatedToken;
                var adminId = int.Parse(jwtToken.Claims.First(x => x.Type == "id").Value);

                return await _context.AdminUsers
                    .FirstOrDefaultAsync(a => a.Id == adminId && a.IsActive);
            }
            catch
            {
                return null;
            }
        }

        public string GenerateJwtToken(AdminUser adminUser)
        {
            var tokenHandler = new JwtSecurityTokenHandler();
            var key = Encoding.ASCII.GetBytes(_configuration["JwtSettings:Secret"] ?? "");
            
            var tokenDescriptor = new SecurityTokenDescriptor
            {
                Subject = new ClaimsIdentity(new[]
                {
                    new Claim("id", adminUser.Id.ToString()),
                    new Claim("username", adminUser.Username),
                    new Claim("email", adminUser.Email),
                    new Claim("role", adminUser.Role),
                    new Claim(ClaimTypes.Role, adminUser.Role)
                }),
                Expires = DateTime.UtcNow.AddMinutes(_configuration.GetValue<int>("JwtSettings:ExpiryMinutes", 60)),
                Issuer = _configuration["JwtSettings:Issuer"],
                Audience = _configuration["JwtSettings:Audience"],
                SigningCredentials = new SigningCredentials(new SymmetricSecurityKey(key), SecurityAlgorithms.HmacSha256Signature)
            };

            var token = tokenHandler.CreateToken(tokenDescriptor);
            return tokenHandler.WriteToken(token);
        }

        public (string hashedPassword, string salt) HashPassword(string password, string? salt = null)
        {
            if (salt == null)
            {
                var saltBytes = new byte[32];
                using (var rng = RandomNumberGenerator.Create())
                {
                    rng.GetBytes(saltBytes);
                }
                salt = Convert.ToBase64String(saltBytes);
            }

            using (var pbkdf2 = new Rfc2898DeriveBytes(password, Convert.FromBase64String(salt), 10000, HashAlgorithmName.SHA256))
            {
                var hash = pbkdf2.GetBytes(32);
                return (Convert.ToBase64String(hash), salt);
            }
        }

        public bool VerifyPassword(string password, string hashedPassword, string salt)
        {
            var (computedHash, _) = HashPassword(password, salt);
            return computedHash == hashedPassword;
        }

        public async Task<bool> AdminExistsAsync(string username)
        {
            return await _context.AdminUsers
                .AnyAsync(a => a.Username == username || a.Email == username);
        }

        public async Task<bool> InitializeDefaultAdminAsync()
        {
            // Check if any admin users exist
            var adminCount = await _context.AdminUsers.CountAsync();
            
            if (adminCount > 0)
            {
                return false; // Admins already exist
            }

<<<<<<< HEAD
            // Create default admin from environment variables or defaults
            var defaultUsername = Environment.GetEnvironmentVariable("YENDOR_DEFAULT_ADMIN_USERNAME") ?? "admin";
            var defaultEmail = Environment.GetEnvironmentVariable("YENDOR_DEFAULT_ADMIN_EMAIL") ?? "<EMAIL>";
            var defaultPassword = Environment.GetEnvironmentVariable("YENDOR_DEFAULT_ADMIN_PASSWORD") ?? "YendorAdmin123!";

            await CreateAdminAsync(defaultUsername, defaultEmail, defaultPassword, AdminRoles.SuperAdmin);

            _logger.LogInformation("Created default admin user: {Username}", defaultUsername);
            return true;
=======
            try 
            {
                // Create default admin from environment variables or generate secure defaults
                var defaultUsername = Environment.GetEnvironmentVariable("YENDOR_DEFAULT_ADMIN_USERNAME") ?? "admin";
                var defaultEmail = Environment.GetEnvironmentVariable("YENDOR_DEFAULT_ADMIN_EMAIL") ?? "<EMAIL>";
                
                // Check if password is provided or generate a secure one
                string defaultPassword;
                if (!string.IsNullOrEmpty(Environment.GetEnvironmentVariable("YENDOR_DEFAULT_ADMIN_PASSWORD")))
                {
                    defaultPassword = Environment.GetEnvironmentVariable("YENDOR_DEFAULT_ADMIN_PASSWORD")!;
                    _logger.LogInformation("Using admin password from environment variable");
                }
                else
                {
                    // Generate a secure random password
                    defaultPassword = GenerateSecurePassword();
                    _logger.LogWarning("Generated secure admin password: {Password} - SAVE THIS PASSWORD!", defaultPassword);
                    _logger.LogWarning("This password will not be shown again. Please change it after first login.");
                    
                    // For container environments, output to console as well to capture in logs
                    Console.WriteLine($"IMPORTANT: Generated secure admin password: {defaultPassword}");
                    Console.WriteLine("Please save this password - it will not be shown again!");
                }

                await CreateAdminAsync(defaultUsername, defaultEmail, defaultPassword, AdminRoles.SuperAdmin);
                _logger.LogInformation("Created default admin user: {Username}", defaultUsername);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to initialize default admin user");
                return false;
            }
        }

        /// <summary>
        /// Generates a cryptographically secure random password for the initial admin user
        /// </summary>
        /// <returns>A secure random password</returns>
        private string GenerateSecurePassword()
        {
            const string upperChars = "ABCDEFGHJKLMNPQRSTUVWXYZ";  // excluding I and O to avoid confusion
            const string lowerChars = "abcdefghijkmnopqrstuvwxyz";  // excluding l to avoid confusion
            const string numericChars = "23456789";  // excluding 0 and 1 to avoid confusion
            const string specialChars = "!@#$%^&*()_-+=<>?";
            
            // Create an array of character groups to ensure we have at least one of each
            var charGroups = new[] { upperChars, lowerChars, numericChars, specialChars };
            
            using var rng = RandomNumberGenerator.Create();
            var passwordChars = new List<char>();
            
            // Add at least one character from each group
            foreach (var group in charGroups)
            {
                var buffer = new byte[1];
                rng.GetBytes(buffer);
                passwordChars.Add(group[buffer[0] % group.Length]);
            }
            
            // Add additional random characters to reach desired length (16 chars)
            var allChars = upperChars + lowerChars + numericChars + specialChars;
            for (int i = passwordChars.Count; i < 16; i++)
            {
                var buffer = new byte[1];
                rng.GetBytes(buffer);
                passwordChars.Add(allChars[buffer[0] % allChars.Length]);
            }
            
            // Shuffle the password characters
            for (int i = passwordChars.Count - 1; i > 0; i--)
            {
                var buffer = new byte[1];
                rng.GetBytes(buffer);
                int j = buffer[0] % (i + 1);
                
                // Swap characters
                var temp = passwordChars[i];
                passwordChars[i] = passwordChars[j];
                passwordChars[j] = temp;
            }
            
            return new string(passwordChars.ToArray());
>>>>>>> dcb0913 (cat profile implementation. project dockerisation and docker container debugging)
        }
    }
}
