using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.EntityFrameworkCore;
using Microsoft.IdentityModel.Tokens;
using Microsoft.OpenApi.Models;
using Serilog;
using System.Text;
using YendorCats.API.Configuration;
using YendorCats.API.Data;
using YendorCats.API.Middleware;
using YendorCats.API.Services;
using System.Reflection;
using Amazon.S3;
using System.Text.Json;
using Pomelo.EntityFrameworkCore.MySql.Infrastructure;

var builder = WebApplication.CreateBuilder(args);

// Configure Serilog
Log.Logger = new LoggerConfiguration()
    .ReadFrom.Configuration(builder.Configuration)
    .Enrich.FromLogContext()
    .WriteTo.Console()
    .CreateLogger();

builder.Host.UseSerilog();

// Add services to the container
builder.Services.AddControllers();

<<<<<<< HEAD
// Register Secrets Manager Service (using HashiCorp Vault)
builder.Services.AddSingleton<ISecretsManagerService, SecretsManagerService>();

// Configure S3 client with fallback mechanism
=======
// Temporarily disable Secrets Manager Service to debug startup hang
// TODO: Re-enable after resolving startup issues
Log.Information("Secrets Manager Service temporarily disabled for debugging");
// builder.Services.AddSingleton<ISecretsManagerService, SecretsManagerService>();

// Configure S3 client using environment variables (no Vault dependency)
Log.Information("Configuring S3 services with environment variables");
>>>>>>> dcb0913 (cat profile implementation. project dockerisation and docker container debugging)
builder.Services.AddSingleton<IAmazonS3>(sp =>
{
    var s3Config = new AmazonS3Config
    {
        ServiceURL = builder.Configuration["AWS:S3:ServiceUrl"],
        ForcePathStyle = true,
    };

    string s3AccessKey = null;
    string s3SecretKey = null;

<<<<<<< HEAD
    // First, try to get credentials from environment variables (most secure)
    s3AccessKey = Environment.GetEnvironmentVariable("YENDOR_S3_ACCESS_KEY");
    s3SecretKey = Environment.GetEnvironmentVariable("YENDOR_S3_SECRET_KEY");

    if (!string.IsNullOrEmpty(s3AccessKey) && !string.IsNullOrEmpty(s3SecretKey))
    {
        Log.Information("Using S3 credentials from environment variables.");
        return new AmazonS3Client(s3AccessKey, s3SecretKey, s3Config);
    }

    // Fallback to appsettings (deprecated - for development only)
=======
    // Primary: Get credentials from appsettings (Docker environment variables)
>>>>>>> dcb0913 (cat profile implementation. project dockerisation and docker container debugging)
    s3AccessKey = builder.Configuration["AWS:S3:AccessKey"];
    s3SecretKey = builder.Configuration["AWS:S3:SecretKey"];

    if (!string.IsNullOrEmpty(s3AccessKey) && !string.IsNullOrEmpty(s3SecretKey))
    {
<<<<<<< HEAD
        Log.Warning("Using S3 credentials from appsettings - consider using environment variables for security.");
        return new AmazonS3Client(s3AccessKey, s3SecretKey, s3Config);
    }

    // If UseCredentialsFromSecrets is true, try to get from Vault
    var useCredentialsFromSecrets = builder.Configuration.GetValue<bool>("AWS:UseCredentialsFromSecrets");
    if (useCredentialsFromSecrets)
    {
        try
        {
            var secretsManager = sp.GetRequiredService<ISecretsManagerService>();
            var secrets = secretsManager.GetAppSecretsAsync().GetAwaiter().GetResult();

            if (!string.IsNullOrEmpty(secrets.S3AccessKey) && !string.IsNullOrEmpty(secrets.S3SecretKey))
            {
                Log.Information("Using S3 credentials from Vault/Secrets Manager.");
                return new AmazonS3Client(secrets.S3AccessKey, secrets.S3SecretKey, s3Config);
            }
            else
            {
                Log.Warning("S3 credentials not found in Vault/Secrets Manager.");
            }
        }
        catch (Exception ex)
        {
            Log.Error(ex, "Failed to get S3 credentials from Secrets Manager, falling back to environment variables or default credentials");
        }
    }

    // Fallback to environment variables
    var envAccessKey = Environment.GetEnvironmentVariable("AWS_ACCESS_KEY_ID");
    var envSecretKey = Environment.GetEnvironmentVariable("AWS_SECRET_ACCESS_KEY");

    if (!string.IsNullOrEmpty(envAccessKey) && !string.IsNullOrEmpty(envSecretKey))
    {
        Log.Information("Using S3 credentials from environment variables.");
        return new AmazonS3Client(envAccessKey, envSecretKey, s3Config);
=======
        Log.Information("Using S3 credentials from configuration (Docker environment variables)");
        return new AmazonS3Client(s3AccessKey, s3SecretKey, s3Config);
    }

    // Fallback: Direct environment variables
    s3AccessKey = Environment.GetEnvironmentVariable("AWS_ACCESS_KEY_ID");
    s3SecretKey = Environment.GetEnvironmentVariable("AWS_SECRET_ACCESS_KEY");

    if (!string.IsNullOrEmpty(s3AccessKey) && !string.IsNullOrEmpty(s3SecretKey))
    {
        Log.Information("Using S3 credentials from direct environment variables");
        return new AmazonS3Client(s3AccessKey, s3SecretKey, s3Config);
>>>>>>> dcb0913 (cat profile implementation. project dockerisation and docker container debugging)
    }

    // Final fallback - create client without credentials (will likely fail but prevents startup crash)
    Log.Warning("No S3 credentials found. Creating S3 client without credentials - S3 operations will likely fail.");
    return new AmazonS3Client(s3Config);
});

// Add S3 storage service
builder.Services.AddScoped<IS3StorageService, S3StorageService>();

// Configure JWT settings
builder.Services.Configure<JwtSettings>(builder.Configuration.GetSection("JwtSettings"));

<<<<<<< HEAD
// Configure Entity Framework
builder.Services.AddDbContext<AppDbContext>((serviceProvider, options) =>
{
    var environment = builder.Environment;
    var secretsManager = serviceProvider.GetRequiredService<ISecretsManagerService>();

    if (environment.IsDevelopment())
    {
        // Temporarily use in-memory database for demo testing
        // TODO: Switch back to MySQL when database is set up
        Log.Information("Using in-memory database for demo testing (no persistence)");
        options.UseInMemoryDatabase("YendorCatsDemo");

        // Commented out MySQL connection for demo
        /*
        var connectionString = builder.Configuration.GetConnectionString("DefaultConnection");
=======
// Configure Entity Framework with SQLite
builder.Services.AddDbContext<AppDbContext>(options =>
{
    var environment = builder.Environment;
    var configuration = builder.Configuration;

    // Get connection string from configuration or use default
    string sqliteConnectionString;

    if (environment.IsDevelopment())
    {
        // Use local path for development
        sqliteConnectionString = configuration.GetConnectionString("SqliteConnection")
            ?? "Data Source=Data/yendorcats.db";
        Log.Information("Using SQLite database for development with persistence: {ConnectionString}", sqliteConnectionString);
    }
    else
    {
        // Use container path for production/staging
        sqliteConnectionString = configuration.GetConnectionString("SqliteConnection")
            ?? "Data Source=/app/data/yendorcats.db";
        Log.Information("Using SQLite database for production with persistence: {ConnectionString}", sqliteConnectionString);

        // Ensure the database directory exists
        var dbPath = sqliteConnectionString.Replace("Data Source=", "");
        var dbDir = Path.GetDirectoryName(dbPath);

        if (!string.IsNullOrEmpty(dbDir) && !Directory.Exists(dbDir))
        {
            try
            {
                Directory.CreateDirectory(dbDir);
                Log.Information("Created database directory: {DbDir}", dbDir);
            }
            catch (Exception ex)
            {
                Log.Warning(ex, "Failed to create database directory: {DbDir}", dbDir);
            }
        }
    }

    // Configure SQLite with connection string
    options.UseSqlite(
        sqliteConnectionString,
        sqliteOptions =>
        {
            sqliteOptions.MigrationsAssembly(typeof(AppDbContext).Assembly.FullName);
            // Enable connection resilience
            sqliteOptions.CommandTimeout(30);
        });

    // Commented out MySQL connection for future migration if needed
    /*
    if (environment.IsDevelopment())
    {
        var connectionString = configuration.GetConnectionString("DefaultConnection");
>>>>>>> dcb0913 (cat profile implementation. project dockerisation and docker container debugging)
        if (string.IsNullOrEmpty(connectionString))
        {
            throw new InvalidOperationException(
                "Development connection string not found. Please check your appsettings.Development.json file.");
        }
        Log.Information("Using MySQL connection string: {ConnectionString}", connectionString);
<<<<<<< HEAD
        options.UseMySql(connectionString, ServerVersion.AutoDetect(connectionString), mySqlOptions => mySqlOptions.EnableRetryOnFailure());
        */
=======
        options.UseMySql(connectionString, ServerVersion.AutoDetect(connectionString), 
            mySqlOptions => mySqlOptions.EnableRetryOnFailure());
>>>>>>> dcb0913 (cat profile implementation. project dockerisation and docker container debugging)
    }
    else
    {
        // Use AWS Secrets Manager in production
        try
        {
            // Get DB credentials from AWS Secrets Manager
            var secrets = secretsManager.GetAppSecretsAsync().GetAwaiter().GetResult();
<<<<<<< HEAD
            options.UseMySql(secrets.DbConnectionString, ServerVersion.AutoDetect(secrets.DbConnectionString), mySqlOptions => mySqlOptions.EnableRetryOnFailure());
        }
    catch (Exception ex)
    {
        Log.Error(ex, "Failed to retrieve database connection from HashiCorp Vault");
        // Fall back to configuration if available
        var connectionString = builder.Configuration.GetConnectionString("ProductionConnection");
        if (string.IsNullOrEmpty(connectionString))
        {
            throw new InvalidOperationException(
                "Failed to get connection string from Vault and no fallback connection string found.");
        }
        options.UseMySql(connectionString, ServerVersion.AutoDetect(connectionString), mySqlOptions => mySqlOptions.EnableRetryOnFailure());
    }
    }
=======
            options.UseMySql(secrets.DbConnectionString, ServerVersion.AutoDetect(secrets.DbConnectionString), 
                mySqlOptions => mySqlOptions.EnableRetryOnFailure());
        }
        catch (Exception ex)
        {
            Log.Error(ex, "Failed to retrieve database connection from HashiCorp Vault");
            // Fall back to configuration if available
            var connectionString = configuration.GetConnectionString("ProductionConnection");
            if (string.IsNullOrEmpty(connectionString))
            {
                throw new InvalidOperationException(
                    "Failed to get connection string from Vault and no fallback connection string found.");
            }
            options.UseMySql(connectionString, ServerVersion.AutoDetect(connectionString), 
                mySqlOptions => mySqlOptions.EnableRetryOnFailure());
        }
    }
    */
>>>>>>> dcb0913 (cat profile implementation. project dockerisation and docker container debugging)
});

// Configure Swagger/OpenAPI
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen(c =>
{
    c.SwaggerDoc("v1", new OpenApiInfo
    {
        Title = "Yendor Cats API",
        Version = "v1",
        Description = "API for the Yendor Cats exotic cat breeder website.",
        Contact = new OpenApiContact
        {
            Name = "Admin",
            Email = "<EMAIL>"
        }
    });

    // Include XML comments in Swagger
    var xmlFile = $"{Assembly.GetExecutingAssembly().GetName().Name}.xml";
    var xmlPath = Path.Combine(AppContext.BaseDirectory, xmlFile);
    c.IncludeXmlComments(xmlPath);

    // Add JWT authentication to Swagger UI
    c.AddSecurityDefinition("Bearer", new OpenApiSecurityScheme
    {
        Description = "JWT Authorization header using the Bearer scheme. Example: \"Authorization: Bearer {token}\"",
        Name = "Authorization",
        In = ParameterLocation.Header,
        Type = SecuritySchemeType.ApiKey,
        Scheme = "Bearer"
    });

    c.AddSecurityRequirement(new OpenApiSecurityRequirement
    {
        {
            new OpenApiSecurityScheme
            {
                Reference = new OpenApiReference
                {
                    Type = ReferenceType.SecurityScheme,
                    Id = "Bearer"
                }
            },
            Array.Empty<string>()
        }
    });
});

// Configure CORS
builder.Services.AddCors(options =>
{
    options.AddPolicy("AllowSpecificOrigins",
        policy =>
        {
            policy.WithOrigins(
                "http://localhost:5000",
                "https://yendorcats.com")
                .AllowAnyMethod()
                .AllowAnyHeader()
                .AllowCredentials();
        });
});

// Configure JWT authentication
builder.Services.AddAuthentication(x =>
{
    x.DefaultAuthenticateScheme = JwtBearerDefaults.AuthenticationScheme;
    x.DefaultChallengeScheme = JwtBearerDefaults.AuthenticationScheme;
})
.AddJwtBearer(options =>
{
<<<<<<< HEAD
    var secretsManager = builder.Services.BuildServiceProvider().GetRequiredService<ISecretsManagerService>();
    AppSecrets secrets;

    try
    {
        // Try to get from secrets manager
        secrets = secretsManager.GetAppSecretsAsync().GetAwaiter().GetResult();
    }
    catch (Exception ex)
    {
        Log.Error(ex, "Failed to get JWT settings from HashiCorp Vault, using values from appsettings");
        // Fallback to appsettings
        var jwtSettings = builder.Configuration.GetSection("JwtSettings");
        var secretKey = jwtSettings["Secret"] ?? "YendorCatsDefaultSecretKey1234567890123456789012"; // Fallback key for dev

        options.TokenValidationParameters = new TokenValidationParameters
        {
            ValidateIssuerSigningKey = true,
            IssuerSigningKey = new SymmetricSecurityKey(Encoding.ASCII.GetBytes(secretKey)),
            ValidateIssuer = !string.IsNullOrEmpty(jwtSettings["Issuer"]),
            ValidIssuer = jwtSettings["Issuer"],
            ValidateAudience = !string.IsNullOrEmpty(jwtSettings["Audience"]),
            ValidAudience = jwtSettings["Audience"],
            ClockSkew = TimeSpan.Zero
        };

        return;
    }

    // Using values from secrets manager
=======
    // Use appsettings for JWT configuration to avoid blocking calls during startup
    var jwtSettings = builder.Configuration.GetSection("JwtSettings");
    var secretKey = jwtSettings["Secret"] ?? "YendorCatsDefaultSecretKey1234567890123456789012"; // Fallback key for dev

    Log.Information("Configuring JWT authentication with appsettings values");

>>>>>>> dcb0913 (cat profile implementation. project dockerisation and docker container debugging)
    options.RequireHttpsMetadata = false;
    options.SaveToken = true;
    options.TokenValidationParameters = new TokenValidationParameters
    {
        ValidateIssuerSigningKey = true,
<<<<<<< HEAD
        IssuerSigningKey = new SymmetricSecurityKey(Encoding.ASCII.GetBytes(secrets.JwtSecret)),
        ValidateIssuer = !string.IsNullOrEmpty(secrets.JwtIssuer),
        ValidIssuer = secrets.JwtIssuer,
        ValidateAudience = !string.IsNullOrEmpty(secrets.JwtAudience),
        ValidAudience = secrets.JwtAudience,
=======
        IssuerSigningKey = new SymmetricSecurityKey(Encoding.ASCII.GetBytes(secretKey)),
        ValidateIssuer = !string.IsNullOrEmpty(jwtSettings["Issuer"]),
        ValidIssuer = jwtSettings["Issuer"],
        ValidateAudience = !string.IsNullOrEmpty(jwtSettings["Audience"]),
        ValidAudience = jwtSettings["Audience"],
>>>>>>> dcb0913 (cat profile implementation. project dockerisation and docker container debugging)
        ClockSkew = TimeSpan.Zero
    };
});

// Register services
builder.Services.AddScoped<ICatService, CatService>();
builder.Services.AddScoped<IUserService, UserService>();
builder.Services.AddScoped<IAuthService, AuthService>();
builder.Services.AddScoped<IImageService, ImageService>();
builder.Services.AddScoped<IImageMetadataService, ImageMetadataService>();
builder.Services.AddScoped<IAdminAuthService, AdminAuthService>();
builder.Services.AddScoped<IPhotoIndexService, PhotoIndexService>();
<<<<<<< HEAD
=======
builder.Services.AddScoped<ICatMetadataService, CatMetadataService>();
>>>>>>> dcb0913 (cat profile implementation. project dockerisation and docker container debugging)

var app = builder.Build();

// Configure middleware pipeline
if (app.Environment.IsDevelopment())
{
    app.UseSwagger();
    app.UseSwaggerUI();
    app.UseDeveloperExceptionPage();
}
else
{
    // Custom exception handling for production
    app.UseMiddleware<ErrorHandlingMiddleware>();
    app.UseHsts();
}

// Configure S3 CORS on startup (temporarily disabled for testing)
// TODO: Fix CORS configuration to use correct credentials
/*
if (!string.IsNullOrEmpty(builder.Configuration["AWS:S3:BucketName"]))
{
    try
    {
        using (var scope = app.Services.CreateScope())
        {
            var s3Service = scope.ServiceProvider.GetRequiredService<IS3StorageService>();
            s3Service.ConfigureCorsAsync().GetAwaiter().GetResult();
            Log.Information("S3 CORS configuration applied successfully");
        }
    }
    catch (Exception ex)
    {
        Log.Error(ex, "Failed to configure S3 CORS on startup");
    }
}
*/

// Apply migrations automatically in development
if (app.Environment.IsDevelopment())
{
<<<<<<< HEAD
    using (var scope = app.Services.CreateScope())
    {
        var dbContext = scope.ServiceProvider.GetRequiredService<AppDbContext>();

        // Only run migrations if not using in-memory database
        if (!dbContext.Database.IsInMemory())
        {
            dbContext.Database.Migrate();
        }
        else
        {
            // For in-memory database, ensure it's created
            dbContext.Database.EnsureCreated();
        }

        // Initialize default admin user if none exist
        var adminAuthService = scope.ServiceProvider.GetRequiredService<IAdminAuthService>();
        await adminAuthService.InitializeDefaultAdminAsync();
=======
    try
    {
        using (var scope = app.Services.CreateScope())
        {
            var dbContext = scope.ServiceProvider.GetRequiredService<AppDbContext>();

            Log.Information("Starting database migration...");
            // Only run migrations if not using in-memory database
            if (!dbContext.Database.IsInMemory())
            {
                dbContext.Database.Migrate();
                Log.Information("Database migration completed successfully");
            }
            else
            {
                // For in-memory database, ensure it's created
                dbContext.Database.EnsureCreated();
                Log.Information("In-memory database created successfully");
            }

            // Initialize default admin user if none exist
            Log.Information("Initializing default admin user...");
            var adminAuthService = scope.ServiceProvider.GetRequiredService<IAdminAuthService>();
            await adminAuthService.InitializeDefaultAdminAsync();
            Log.Information("Admin user initialization completed");
        }
    }
    catch (Exception ex)
    {
        Log.Error(ex, "Failed to initialize database or admin user");
        // Continue startup even if database initialization fails
>>>>>>> dcb0913 (cat profile implementation. project dockerisation and docker container debugging)
    }
}

// Only use HTTPS redirection in production
if (!app.Environment.IsDevelopment())
{
    app.UseHttpsRedirection();
}

// Configure static files with default file support
app.UseDefaultFiles(new DefaultFilesOptions
{
    DefaultFileNames = new List<string> { "index.html" }
});
app.UseFileServer();

// Add SPA fallback route handler
app.MapFallbackToFile("index.html");

// Enable CORS
app.UseCors("AllowSpecificOrigins");

// Authentication & Authorization
app.UseAuthentication();
app.UseAuthorization();

// Configure file watcher in development mode for real-time updates
if (app.Environment.IsDevelopment())
{
    app.UseLiveReload();
<<<<<<< HEAD
    app.UseFileWatcher(app.Environment, app.Services.GetRequiredService<ILogger<FileSystemWatcher>>());
=======
    // Temporarily disable file watcher for debugging
    // app.UseFileWatcher(app.Environment, app.Services.GetRequiredService<ILogger<FileSystemWatcher>>());
    Log.Information("File watcher temporarily disabled for debugging");
>>>>>>> dcb0913 (cat profile implementation. project dockerisation and docker container debugging)
}

app.MapControllers();

try
{
    Log.Information("Starting Yendor Cats API");
<<<<<<< HEAD
    app.Run("http://localhost:5002");
=======
    app.Run();
>>>>>>> dcb0913 (cat profile implementation. project dockerisation and docker container debugging)
}
catch (Exception ex)
{
    Log.Fatal(ex, "Application start-up failed");
}
finally
{
    Log.CloseAndFlush();
}
