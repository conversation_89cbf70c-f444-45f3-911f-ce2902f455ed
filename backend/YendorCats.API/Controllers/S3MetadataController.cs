using Microsoft.AspNetCore.Mvc;
using YendorCats.API.Attributes;
using YendorCats.API.Services;
using System.ComponentModel.DataAnnotations;

namespace YendorCats.API.Controllers
{
    /// <summary>
    /// Controller for managing S3 object metadata
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    [AdminAuthorize("SuperAdmin", "Admin", "Editor")]
    public class S3MetadataController : ControllerBase
    {
        private readonly IS3StorageService _s3StorageService;
        private readonly ILogger<S3MetadataController> _logger;

        public S3MetadataController(
            IS3StorageService s3StorageService,
            ILogger<S3MetadataController> logger)
        {
            _s3StorageService = s3StorageService;
            _logger = logger;
        }

        /// <summary>
        /// Update metadata for an S3 object
        /// </summary>
        /// <param name="request">Metadata update request</param>
        /// <returns>Success response</returns>
        [HttpPost("update")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> UpdateMetadata([FromBody] S3MetadataUpdateRequest request)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                var adminUser = HttpContext.GetAdminUser();
                _logger.LogInformation("Admin {Username} updating metadata for S3 key: {S3Key}", 
                    adminUser?.Username, request.S3Key);

                // Prepare metadata dictionary
                var metadata = new Dictionary<string, string>();

                if (!string.IsNullOrEmpty(request.CatName))
                    metadata["cat-name"] = request.CatName;

                if (!string.IsNullOrEmpty(request.Age))
                    metadata["age"] = request.Age;

                if (!string.IsNullOrEmpty(request.DateTaken))
                    metadata["date-taken"] = request.DateTaken;

                if (!string.IsNullOrEmpty(request.Description))
                    metadata["description"] = request.Description;

                if (!string.IsNullOrEmpty(request.Breed))
                    metadata["breed"] = request.Breed;

                if (!string.IsNullOrEmpty(request.Gender))
                    metadata["gender"] = request.Gender;

                if (!string.IsNullOrEmpty(request.Color))
                    metadata["color"] = request.Color;

                if (!string.IsNullOrEmpty(request.Personality))
                    metadata["personality"] = request.Personality;

                if (!string.IsNullOrEmpty(request.Bloodline))
                    metadata["bloodline"] = request.Bloodline;

        // TODO: Review merge conflict - resolve conflicting implementations
        // <<<<<<< HEAD
        // =======
                // Pedigree-specific fields
                if (!string.IsNullOrEmpty(request.CatId))
                    metadata["cat-id"] = request.CatId;

                if (!string.IsNullOrEmpty(request.RegisteredName))
                    metadata["registered-name"] = request.RegisteredName;

                if (!string.IsNullOrEmpty(request.RegistrationNumber))
                    metadata["registration-number"] = request.RegistrationNumber;

                if (!string.IsNullOrEmpty(request.FatherId))
                    metadata["father-id"] = request.FatherId;

                if (!string.IsNullOrEmpty(request.MotherId))
                    metadata["mother-id"] = request.MotherId;

                if (!string.IsNullOrEmpty(request.BreedingStatus))
                    metadata["breeding-status"] = request.BreedingStatus;

                if (!string.IsNullOrEmpty(request.AvailabilityStatus))
                    metadata["availability-status"] = request.AvailabilityStatus;

                if (!string.IsNullOrEmpty(request.PhotoType))
                    metadata["photo-type"] = request.PhotoType;

                if (!string.IsNullOrEmpty(request.AgeAtPhoto))
                    metadata["age-at-photo"] = request.AgeAtPhoto;

                if (!string.IsNullOrEmpty(request.Tags))
                    metadata["tags"] = request.Tags;

                if (!string.IsNullOrEmpty(request.ChampionTitles))
                    metadata["champion-titles"] = request.ChampionTitles;

                if (!string.IsNullOrEmpty(request.GenerationLevel))
                    metadata["generation-level"] = request.GenerationLevel;

        // >>>>>>> dcb0913 (cat profile implementation. project dockerisation and docker container debugging)
                // Add system metadata
                metadata["updated-by"] = adminUser?.Username ?? "system";
                metadata["updated-at"] = DateTime.UtcNow.ToString("yyyy-MM-ddTHH:mm:ssZ");

                // Update S3 object metadata by copying to itself with new metadata
                await UpdateS3ObjectMetadata(request.S3Key, metadata);

                _logger.LogInformation("Successfully updated metadata for S3 key: {S3Key}", request.S3Key);

                return Ok(new { 
                    success = true, 
                    message = "Metadata updated successfully",
                    s3Key = request.S3Key,
                    updatedBy = adminUser?.Username,
                    updatedAt = DateTime.UtcNow
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating metadata for S3 key: {S3Key}", request.S3Key);
                return StatusCode(500, new { 
                    success = false, 
                    message = "An error occurred while updating metadata" 
                });
            }
        }

        /// <summary>
        /// Get current metadata for an S3 object
        /// </summary>
        /// <param name="s3Key">S3 object key</param>
        /// <returns>Current metadata</returns>
        [HttpGet("get/{*s3Key}")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> GetMetadata(string s3Key)
        {
            try
            {
                var metadata = await _s3StorageService.GetObjectMetadataAsync(s3Key);

                return Ok(new {
                    s3Key = s3Key,
                    metadata = metadata,
                    retrievedAt = DateTime.UtcNow
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving metadata for S3 key: {S3Key}", s3Key);
                return StatusCode(500, new { 
                    success = false, 
                    message = "An error occurred while retrieving metadata" 
                });
            }
        }

        /// <summary>
        /// Bulk update metadata for multiple S3 objects
        /// </summary>
        /// <param name="requests">List of metadata update requests</param>
        /// <returns>Bulk update results</returns>
        [HttpPost("bulk-update")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> BulkUpdateMetadata([FromBody] List<S3MetadataUpdateRequest> requests)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                var adminUser = HttpContext.GetAdminUser();
                var results = new List<object>();

                _logger.LogInformation("Admin {Username} performing bulk metadata update for {Count} objects", 
                    adminUser?.Username, requests.Count);

                foreach (var request in requests)
                {
                    try
                    {
                        var metadata = new Dictionary<string, string>();

                        if (!string.IsNullOrEmpty(request.CatName))
                            metadata["cat-name"] = request.CatName;
                        if (!string.IsNullOrEmpty(request.Age))
                            metadata["age"] = request.Age;
                        if (!string.IsNullOrEmpty(request.DateTaken))
                            metadata["date-taken"] = request.DateTaken;
                        if (!string.IsNullOrEmpty(request.Description))
                            metadata["description"] = request.Description;
                        if (!string.IsNullOrEmpty(request.Breed))
                            metadata["breed"] = request.Breed;
                        if (!string.IsNullOrEmpty(request.Gender))
                            metadata["gender"] = request.Gender;
                        if (!string.IsNullOrEmpty(request.Color))
                            metadata["color"] = request.Color;
                        if (!string.IsNullOrEmpty(request.Personality))
                            metadata["personality"] = request.Personality;
                        if (!string.IsNullOrEmpty(request.Bloodline))
                            metadata["bloodline"] = request.Bloodline;

                        metadata["updated-by"] = adminUser?.Username ?? "system";
                        metadata["updated-at"] = DateTime.UtcNow.ToString("yyyy-MM-ddTHH:mm:ssZ");

                        await UpdateS3ObjectMetadata(request.S3Key, metadata);

                        results.Add(new { 
                            s3Key = request.S3Key, 
                            success = true, 
                            message = "Updated successfully" 
                        });
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Error updating metadata for S3 key: {S3Key}", request.S3Key);
                        results.Add(new { 
                            s3Key = request.S3Key, 
                            success = false, 
                            message = ex.Message 
                        });
                    }

                    // Small delay to avoid rate limiting
                    await Task.Delay(100);
                }

                var successCount = results.Count(r => (bool)r.GetType().GetProperty("success")?.GetValue(r)!);

                return Ok(new {
                    success = true,
                    message = $"Bulk update completed: {successCount}/{requests.Count} successful",
                    results = results,
                    updatedBy = adminUser?.Username,
                    completedAt = DateTime.UtcNow
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during bulk metadata update");
                return StatusCode(500, new { 
                    success = false, 
                    message = "An error occurred during bulk update" 
                });
            }
        }

        private async Task UpdateS3ObjectMetadata(string s3Key, Dictionary<string, string> metadata)
        {
        // TODO: Review merge conflict - resolve conflicting implementations
        // <<<<<<< HEAD
            // Use the S3 storage service to copy the object to itself with new metadata
            // This is the standard way to update metadata without re-uploading the file
            var bucketName = "yendor"; // This should come from configuration
            
            // Note: This is a simplified implementation
            // In a real implementation, you'd use the AWS SDK to copy the object with new metadata
            // For now, we'll use the existing S3StorageService methods
            
            // The actual implementation would be:
            // await _s3StorageService.UpdateObjectMetadataAsync(s3Key, metadata);
            
            // Since we don't have this method yet, we'll log the operation
            _logger.LogInformation("Would update metadata for {S3Key} with {MetadataCount} fields", 
                s3Key, metadata.Count);
            
            // TODO: Implement actual S3 metadata update in S3StorageService
            throw new NotImplementedException("S3 metadata update not yet implemented in S3StorageService");
        // =======
            await _s3StorageService.UpdateObjectMetadataAsync(s3Key, metadata);
        // >>>>>>> dcb0913 (cat profile implementation. project dockerisation and docker container debugging)
        }
    }

    /// <summary>
    /// S3 metadata update request model
    /// </summary>
    public class S3MetadataUpdateRequest
    {
        /// <summary>
        /// S3 object key
        /// </summary>
        [Required]
        public string S3Key { get; set; } = string.Empty;

        /// <summary>
        /// Cat name
        /// </summary>
        public string? CatName { get; set; }

        /// <summary>
        /// Cat age
        /// </summary>
        public string? Age { get; set; }

        /// <summary>
        /// Date photo was taken
        /// </summary>
        public string? DateTaken { get; set; }

        /// <summary>
        /// Photo description
        /// </summary>
        public string? Description { get; set; }

        /// <summary>
        /// Cat breed
        /// </summary>
        public string? Breed { get; set; }

        /// <summary>
        /// Cat gender
        /// </summary>
        public string? Gender { get; set; }

        /// <summary>
        /// Cat color/pattern
        /// </summary>
        public string? Color { get; set; }

        /// <summary>
        /// Cat personality
        /// </summary>
        public string? Personality { get; set; }

        /// <summary>
        /// Cat bloodline
        /// </summary>
        public string? Bloodline { get; set; }
        // TODO: Review merge conflict - resolve conflicting implementations
        // <<<<<<< HEAD
        // =======

        /// <summary>
        /// Unique cat identifier
        /// </summary>
        public string? CatId { get; set; }

        /// <summary>
        /// Official registered name
        /// </summary>
        public string? RegisteredName { get; set; }

        /// <summary>
        /// Registration number from pedigree papers
        /// </summary>
        public string? RegistrationNumber { get; set; }

        /// <summary>
        /// Father's cat ID
        /// </summary>
        public string? FatherId { get; set; }

        /// <summary>
        /// Mother's cat ID
        /// </summary>
        public string? MotherId { get; set; }

        /// <summary>
        /// Breeding status (available-kitten, breeding-queen, stud, retired)
        /// </summary>
        public string? BreedingStatus { get; set; }

        /// <summary>
        /// Availability status (available, reserved, sold, not-for-sale)
        /// </summary>
        public string? AvailabilityStatus { get; set; }

        /// <summary>
        /// Type of photo (profile, action, family, breeding, growth)
        /// </summary>
        public string? PhotoType { get; set; }

        /// <summary>
        /// Age when photo was taken
        /// </summary>
        public string? AgeAtPhoto { get; set; }

        /// <summary>
        /// Searchable tags (comma-separated)
        /// </summary>
        public string? Tags { get; set; }

        /// <summary>
        /// Champion titles and achievements
        /// </summary>
        public string? ChampionTitles { get; set; }

        /// <summary>
        /// Generation level in pedigree (1=parent, 2=grandparent, etc.)
        /// </summary>
        public string? GenerationLevel { get; set; }
        // >>>>>>> dcb0913 (cat profile implementation. project dockerisation and docker container debugging)
    }
}
