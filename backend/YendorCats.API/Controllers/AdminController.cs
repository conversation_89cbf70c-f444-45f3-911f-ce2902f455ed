using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using YendorCats.API.Services;

namespace YendorCats.API.Controllers
{
    /// <summary>
    /// Controller for administrative operations
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    [Authorize(Roles = "Admin")]
    public class AdminController : ControllerBase
    {
        private readonly ILogger<AdminController> _logger;
        private readonly IS3StorageService _s3StorageService;

        /// <summary>
        /// Constructor for the admin controller
        /// </summary>
        /// <param name="logger">Logger for logging operations</param>
        /// <param name="s3StorageService">S3 storage service</param>
        public AdminController(
            ILogger<AdminController> logger,
            IS3StorageService s3StorageService)
        {
            _logger = logger;
            _s3StorageService = s3StorageService;
        }

        /// <summary>
        /// Configure CORS for the S3 bucket
        /// </summary>
        /// <returns>Result of the operation</returns>
        [HttpPost("s3/configure-cors")]
        public async Task<IActionResult> ConfigureS3Cors()
        {
            _logger.LogInformation("Configuring CORS for S3 bucket");
            
            try
            {
                await _s3StorageService.ConfigureCorsAsync();
                return Ok(new { message = "S3 CORS configuration successful" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error configuring CORS for S3 bucket");
                return StatusCode(500, new { message = "Error configuring CORS for S3 bucket", error = ex.Message });
            }
        }

        /// <summary>
        /// Get S3 bucket configuration for admin access with secure credential access
        /// </summary>
        /// <returns>S3 configuration details</returns>
        [HttpGet("s3/config")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> GetS3Configuration()
        {
            var adminUser = HttpContext.GetAdminUser();
            _logger.LogInformation("Admin {Username} requesting S3 configuration", adminUser?.Username);
            
            try
            {
                var config = await _s3StorageService.GetS3ConfigurationAsync();
                
                // Add enhanced configuration for admin operations
                var enhancedConfig = new Dictionary<string, object>(config)
                {
                    ["hasCredentials"] = !string.IsNullOrEmpty(Environment.GetEnvironmentVariable("AWS_ACCESS_KEY_ID")),
                    ["credentialsSource"] = DetermineCredentialsSource(),
                    ["lastConfigCheck"] = DateTime.UtcNow,
                    ["adminAccess"] = new {
                        canUpload = true,
                        canDelete = adminUser?.Role == "SuperAdmin",
                        canModifyMetadata = true,
                        canConfigureCors = adminUser?.Role == "SuperAdmin"
                    }
                };

                return Ok(new { 
                    success = true,
                    s3Config = enhancedConfig,
                    retrievedBy = adminUser?.Username,
                    retrievedAt = DateTime.UtcNow
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving S3 configuration");
                return StatusCode(500, new { 
                    success = false, 
                    message = "Error retrieving S3 configuration", 
                    error = ex.Message 
                });
            }
        }

        /// <summary>
        /// Get S3 access credentials for administrator bucket operations
        /// </summary>
        /// <returns>S3 access credentials (masked for security)</returns>
        [HttpGet("s3/credentials")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> GetS3Credentials()
        {
            var adminUser = HttpContext.GetAdminUser();
            _logger.LogInformation("Admin {Username} requesting S3 credentials", adminUser?.Username);
            
            try
            {
                // Only SuperAdmin can access full credentials
                if (adminUser?.Role != "SuperAdmin")
                {
                    return Forbid("Only SuperAdmin can access S3 credentials");
                }

                var accessKey = Environment.GetEnvironmentVariable("AWS_ACCESS_KEY_ID") 
                    ?? Environment.GetEnvironmentVariable("AWS_S3_ACCESS_KEY");
                var secretKey = Environment.GetEnvironmentVariable("AWS_SECRET_ACCESS_KEY")
                    ?? Environment.GetEnvironmentVariable("AWS_S3_SECRET_KEY");
                
                var hasCredentials = !string.IsNullOrEmpty(accessKey) && !string.IsNullOrEmpty(secretKey);

                var credentialsInfo = new
                {
                    hasCredentials = hasCredentials,
                    accessKeyMasked = hasCredentials ? MaskCredential(accessKey) : null,
                    secretKeyMasked = hasCredentials ? MaskCredential(secretKey) : null,
                    source = DetermineCredentialsSource(),
                    canPerformOperations = hasCredentials,
                    lastChecked = DateTime.UtcNow
                };

                return Ok(new { 
                    success = true,
                    credentials = credentialsInfo,
                    retrievedBy = adminUser?.Username,
                    retrievedAt = DateTime.UtcNow
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving S3 credentials");
                return StatusCode(500, new { 
                    success = false, 
                    message = "Error retrieving S3 credentials" 
                });
            }
        }

        /// <summary>
        /// Test S3 connection and permissions
        /// </summary>
        /// <returns>Connection test results</returns>
        [HttpPost("s3/test-connection")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> TestS3Connection()
        {
            var adminUser = HttpContext.GetAdminUser();
            _logger.LogInformation("Admin {Username} testing S3 connection", adminUser?.Username);
            
            try
            {
                var testResults = new Dictionary<string, object>
                {
                    ["connectionTest"] = "passed",
                    ["timestamp"] = DateTime.UtcNow
                };

                // Test basic S3 operations
                try
                {
                    var files = await _s3StorageService.ListFilesAsync("");
                    testResults["listFiles"] = "passed";
                    testResults["fileCount"] = files.Count;
                }
                catch (Exception ex)
                {
                    testResults["listFiles"] = "failed";
                    testResults["listFilesError"] = ex.Message;
                }

                // Test metadata operations
                try
                {
                    var searchResult = await _s3StorageService.SearchByMetadataAsync(new Dictionary<string, string>());
                    testResults["metadataSearch"] = "passed";
                    testResults["metadataCount"] = searchResult.Count;
                }
                catch (Exception ex)
                {
                    testResults["metadataSearch"] = "failed";
                    testResults["metadataError"] = ex.Message;
                }

                // Test configuration access
                try
                {
                    var config = await _s3StorageService.GetS3ConfigurationAsync();
                    testResults["configAccess"] = "passed";
                    testResults["bucketName"] = config.GetValueOrDefault("bucketName", "unknown");
                }
                catch (Exception ex)
                {
                    testResults["configAccess"] = "failed";
                    testResults["configError"] = ex.Message;
                }

                var overallSuccess = !testResults.Values.Any(v => v.ToString() == "failed");

                return Ok(new { 
                    success = overallSuccess,
                    message = overallSuccess ? "S3 connection test passed" : "S3 connection test failed",
                    testResults = testResults,
                    testedBy = adminUser?.Username,
                    testedAt = DateTime.UtcNow
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error testing S3 connection");
                return StatusCode(500, new { 
                    success = false, 
                    message = "Error testing S3 connection",
                    error = ex.Message
                });
            }
        }

        /// <summary>
        /// List all cats with their metadata from S3
        /// </summary>
        /// <param name="prefix">Optional prefix to filter cats</param>
        /// <returns>List of cats with metadata</returns>
        [HttpGet("cats/list-all")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> ListAllCatsWithMetadata([FromQuery] string? prefix = "")
        {
            _logger.LogInformation("Admin requesting all cats with metadata, prefix: {Prefix}", prefix ?? "none");
            
            try
            {
                var catsWithMetadata = await _s3StorageService.SearchByMetadataAsync(
                    new Dictionary<string, string>(), 
                    prefix ?? ""
                );

                var groupedCats = catsWithMetadata
                    .GroupBy(c => c.Metadata.GetValueOrDefault("cat-name", "Unknown Cat"))
                    .Select(g => new {
                        catName = g.Key,
                        catId = g.First().Metadata.GetValueOrDefault("cat-id", ""),
                        photoCount = g.Count(),
                        photos = g.Select(photo => new {
                            s3Key = photo.S3Object.Key,
                            url = photo.PublicUrl,
                            size = photo.S3Object.Size,
                            lastModified = photo.S3Object.LastModified,
                            metadata = photo.Metadata
                        }).ToList(),
                        breed = g.First().Metadata.GetValueOrDefault("breed", "Maine Coon"),
                        bloodline = g.First().Metadata.GetValueOrDefault("bloodline", ""),
                        breedingStatus = g.First().Metadata.GetValueOrDefault("breeding-status", ""),
                        availabilityStatus = g.First().Metadata.GetValueOrDefault("availability-status", "")
                    })
                    .OrderBy(c => c.catName)
                    .ToList();

                return Ok(new {
                    success = true,
                    totalCats = groupedCats.Count,
                    totalPhotos = catsWithMetadata.Count,
                    cats = groupedCats,
                    retrievedAt = DateTime.UtcNow
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error listing cats with metadata");
                return StatusCode(500, new { 
                    success = false, 
                    message = "Error retrieving cats data" 
                });
            }
        }

        /// <summary>
        /// Get photos that don't have cat metadata assigned
        /// </summary>
        /// <returns>List of unlinked photos</returns>
        [HttpGet("photos/unlinked")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> GetUnlinkedPhotos()
        {
            _logger.LogInformation("Admin requesting unlinked photos");
            
            try
            {
                var allPhotos = await _s3StorageService.SearchByMetadataAsync(
                    new Dictionary<string, string>()
                );

                var unlinkedPhotos = allPhotos
                    .Where(p => string.IsNullOrEmpty(p.Metadata.GetValueOrDefault("cat-name", "")))
                    .Select(p => new {
                        s3Key = p.S3Object.Key,
                        url = p.PublicUrl,
                        size = p.S3Object.Size,
                        lastModified = p.S3Object.LastModified,
                        metadata = p.Metadata
                    })
                    .OrderByDescending(p => p.lastModified)
                    .ToList();

                return Ok(new {
                    success = true,
                    count = unlinkedPhotos.Count,
                    photos = unlinkedPhotos,
                    retrievedAt = DateTime.UtcNow
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving unlinked photos");
                return StatusCode(500, new { 
                    success = false, 
                    message = "Error retrieving unlinked photos" 
                });
            }
        }

        /// <summary>
        /// Search cats by specific metadata criteria
        /// </summary>
        /// <param name="searchRequest">Search criteria</param>
        /// <returns>Matching cats</returns>
        [HttpPost("cats/search")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> SearchCats([FromBody] CatSearchRequest searchRequest)
        {
            _logger.LogInformation("Admin searching cats with criteria: {Criteria}", searchRequest);
            
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                var searchFilters = new Dictionary<string, string>();
                
                if (!string.IsNullOrEmpty(searchRequest.CatName))
                    searchFilters["cat-name"] = searchRequest.CatName;
                if (!string.IsNullOrEmpty(searchRequest.Breed))
                    searchFilters["breed"] = searchRequest.Breed;
                if (!string.IsNullOrEmpty(searchRequest.Bloodline))
                    searchFilters["bloodline"] = searchRequest.Bloodline;
                if (!string.IsNullOrEmpty(searchRequest.BreedingStatus))
                    searchFilters["breeding-status"] = searchRequest.BreedingStatus;

                var matchingPhotos = await _s3StorageService.SearchByMetadataAsync(
                    searchFilters, 
                    searchRequest.Prefix ?? ""
                );

                return Ok(new {
                    success = true,
                    searchCriteria = searchFilters,
                    resultCount = matchingPhotos.Count,
                    photos = matchingPhotos.Select(p => new {
                        s3Key = p.S3Object.Key,
                        url = p.PublicUrl,
                        metadata = p.Metadata
                    }).ToList(),
                    retrievedAt = DateTime.UtcNow
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error searching cats");
                return StatusCode(500, new { 
                    success = false, 
                    message = "Error searching cats" 
                });
            }
        }

        #region Private Helper Methods

        /// <summary>
        /// Determine the source of S3 credentials
        /// </summary>
        /// <returns>Credentials source description</returns>
        private string DetermineCredentialsSource()
        {
            if (!string.IsNullOrEmpty(Environment.GetEnvironmentVariable("AWS_ACCESS_KEY_ID")))
            {
                return "Environment Variables (AWS_ACCESS_KEY_ID)";
            }
            if (!string.IsNullOrEmpty(Environment.GetEnvironmentVariable("AWS_S3_ACCESS_KEY")))
            {
                return "Environment Variables (AWS_S3_ACCESS_KEY)";
            }
            return "No credentials found";
        }

        /// <summary>
        /// Mask a credential string for secure display
        /// </summary>
        /// <param name="credential">Credential to mask</param>
        /// <returns>Masked credential</returns>
        private string MaskCredential(string credential)
        {
            if (string.IsNullOrEmpty(credential))
            {
                return "Not available";
            }

            if (credential.Length <= 8)
            {
                return new string('*', credential.Length);
            }

            var start = credential.Substring(0, 4);
            var end = credential.Substring(credential.Length - 4);
            var middle = new string('*', credential.Length - 8);
            
            return $"{start}{middle}{end}";
        }

        #endregion
    }

    /// <summary>
    /// Search request model for cat metadata search
    /// </summary>
    public class CatSearchRequest
    {
        /// <summary>
        /// Cat name to search for
        /// </summary>
        public string? CatName { get; set; }

        /// <summary>
        /// Breed to filter by
        /// </summary>
        public string? Breed { get; set; }

        /// <summary>
        /// Bloodline to filter by
        /// </summary>
        public string? Bloodline { get; set; }

        /// <summary>
        /// Breeding status to filter by
        /// </summary>
        public string? BreedingStatus { get; set; }

        /// <summary>
        /// S3 prefix to limit search scope
        /// </summary>
        public string? Prefix { get; set; }
    }
}
